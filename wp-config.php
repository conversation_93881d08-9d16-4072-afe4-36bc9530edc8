<?php
/**
 * The base configuration for WordPress
 *
 * The wp-config.php creation script uses this file during the installation.
 * You don't have to use the website, you can copy this file to "wp-config.php"
 * and fill in the values.
 *
 * This file contains the following configurations:
 *
 * * Database settings
 * * Secret keys
 * * Database table prefix
 * * ABSPATH
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/
 *
 * @package WordPress
 */

// ** Database settings - You can get this info from your web host ** //
/** The name of the database for WordPress */
define( 'DB_NAME', 'wp_sodexo_juni25' );

/** Database username */
define( 'DB_USER', 'wp_sodexo_juni25' );

/** Database password */
define( 'DB_PASSWORD', 'ydGG8U5V_kjsd7NAc8F' );

/** Database hostname */
define( 'DB_HOST', 'localhost' );

/** Database charset to use in creating database tables. */
define( 'DB_CHARSET', 'utf8mb4' );

/** The database collate type. Don't change this if in doubt. */
define( 'DB_COLLATE', '' );

/**#@+
 * Authentication unique keys and salts.
 *
 * Change these to different unique phrases! You can generate these using
 * the {@link https://api.wordpress.org/secret-key/1.1/salt/ WordPress.org secret-key service}.
 *
 * You can change these at any point in time to invalidate all existing cookies.
 * This will force all users to have to log in again.
 *
 * @since 2.6.0
 */
define( 'AUTH_KEY',         '5zmu6q,!W$Z2CFVn8]Fro(HS`;&P.WyW;,`EHOY6kLlYREi6p$?LtFk~!ulY9o=g' );
define( 'SECURE_AUTH_KEY',  ')2*Oua[,d,ZPInPfvj1;dZoDobg(v2wg.TYI][x4#(VkrB#Y{sQss_$_WjOQzcu5' );
define( 'LOGGED_IN_KEY',    '|10pT<MR?&PR+d:%zg%L@d/$ $(1Ot;$|>w;3#tt-%.2G)zPbZ2%O|GqwJQ9U`7X' );
define( 'NONCE_KEY',        'Ct/qlFXCzI#.r_fC%me+;C_,nMQg: hqK9@qqj~Cguy0jL]yLfpU41/}Xd#s?9|D' );
define( 'AUTH_SALT',        ':Z^{tI2F-fYdl?%m:KOOYh!Du{W+dM#=[YVXwTj.)RQ -aa?;fug/]G< )rTRe~c' );
define( 'SECURE_AUTH_SALT', '[Ks!2b#!_PXzw=Qh)Yfm4}2ISKiG!=Fo ?%zkG-9WH^ N-A-=$tyQ$4NG`NkU^3~' );
define( 'LOGGED_IN_SALT',   'i9*v2$9j4i]!k;g|~w%)MWfb|m$i@XRW2EE9pDC~,?exNUbLc48D)C].P-`}_Cg&' );
define( 'NONCE_SALT',       'YFR7>|-thr(W<QcCtuXr0:@xw,{UM*dLETm^IB0:5 &DQ=^,XOgGJ^mtouwg>hb~' );

/**#@-*/

/**
 * WordPress database table prefix.
 *
 * You can have multiple installations in one database if you give each
 * a unique prefix. Only numbers, letters, and underscores please!
 *
 * At the installation time, database tables are created with the specified prefix.
 * Changing this value after WordPress is installed will make your site think
 * it has not been installed.
 *
 * @link https://developer.wordpress.org/advanced-administration/wordpress/wp-config/#table-prefix
 */
$table_prefix = 'aichat_';

/**
 * For developers: WordPress debugging mode.
 *
 * Change this to true to enable the display of notices during development.
 * It is strongly recommended that plugin and theme developers use WP_DEBUG
 * in their development environments.
 *
 * For information on other constants that can be used for debugging,
 * visit the documentation.
 *
 * @link https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/
 */
define( 'WP_DEBUG', false );

/* Add any custom values between this line and the "stop editing" line. */

/**
 * Sodexo AI Crawler Plugin Settings
 * Configuration constants for the cron_ai_crawl plugin
 */

// Mistral AI Configuration
define( 'MISTRAL_AI_API_KEY', '' ); // Add your Mistral AI API key here
define( 'MISTRAL_AI_MODEL', 'mistral-tiny' ); // Default model for AI requests
define( 'MISTRAL_AI_TEMPERATURE', 0.7 ); // Temperature for AI responses (0.0 to 1.0)
define( 'MISTRAL_AI_MAX_TOKENS', 2000 ); // Maximum tokens per request

// Crawler Configuration
define( 'SODEXO_CRAWLER_ENABLED', true ); // Enable/disable the crawler functionality
define( 'SODEXO_CRAWLER_INTERVAL', 'hourly' ); // Cron interval: hourly, daily, weekly
define( 'SODEXO_CRAWLER_TIMEOUT', 30 ); // HTTP request timeout in seconds
define( 'SODEXO_CRAWLER_USER_AGENT', 'Sodexo AI Crawler/1.0' ); // User agent for web requests

// Logging Configuration
define( 'SODEXO_CRAWLER_LOG_LEVEL', 'info' ); // Log level: debug, info, warning, error
define( 'SODEXO_CRAWLER_LOG_RETENTION_DAYS', 30 ); // Days to keep log files
define( 'SODEXO_CRAWLER_ENABLE_DEBUG', false ); // Enable debug mode for detailed logging

// Database Configuration
define( 'SODEXO_CRAWLER_BATCH_SIZE', 10 ); // Number of URLs to process per batch
define( 'SODEXO_CRAWLER_MAX_RETRIES', 3 ); // Maximum retry attempts for failed requests

// Security Configuration
define( 'SODEXO_CRAWLER_ALLOWED_DOMAINS', 'sodexo.com,partner-sites.com' ); // Comma-separated allowed domains
define( 'SODEXO_CRAWLER_RATE_LIMIT', 5 ); // Requests per minute rate limit



/* That's all, stop editing! Happy publishing. */

/** Absolute path to the WordPress directory. */
if ( ! defined( 'ABSPATH' ) ) {
	define( 'ABSPATH', __DIR__ . '/' );
}

/** Sets up WordPress vars and included files. */
require_once ABSPATH . 'wp-settings.php';
