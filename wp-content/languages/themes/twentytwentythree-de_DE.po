# Translation of Themes - Twenty Twenty-Three in German
# This file is distributed under the same license as the Themes - Twenty Twenty-Three package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-08-16 18:58:11+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: de\n"
"Project-Id-Version: Themes - Twenty Twenty-Three\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three is designed to take advantage of the new design tools introduced in WordPress 6.1. With a clean, blank base as a starting point, this default theme includes ten diverse style variations created by members of the WordPress community. Whether you want to build a complex or incredibly simple website, you can do it quickly and intuitively through the bundled styles or dive into creation and full customization yourself."
msgstr "Twenty Twenty-Three wurde entwickelt, um die Vorteile der neuen Design-Werkzeuge zu nutzen, die mit WordPress 6.1 eingeführt wurden. Mit einer sauberen, leeren Basis als Ausgangspunkt enthält dieses Standard-Theme zehn verschiedene Stilvarianten, die von Mitgliedern der WordPress-Community erstellt wurden. Ganz gleich, ob du eine komplexe oder eine unglaublich einfache Website erstellen möchtest, du kannst dies schnell und intuitiv mithilfe der mitgelieferten Stile tun oder selbst in die Erstellung und vollständige Anpassung einsteigen."

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three"
msgstr "Twenty Twenty-Three"

#: patterns/hidden-heading.php:9
msgctxt "Main heading for homepage"
msgid "Mindblown: a blog about philosophy."
msgstr "Atemberaubend: ein Blog über Philosophie."

#: patterns/hidden-heading.php
msgctxt "Pattern title"
msgid "Hidden Heading for Homepage"
msgstr "Versteckte Überschrift für die Homepage"

#: patterns/post-meta.php
msgctxt "Pattern description"
msgid "Post meta information with separator on the top."
msgstr "Beitrags-Meta-Informationen mit einem Trennzeichen am oberen Rand."

#: patterns/footer-default.php
msgctxt "Pattern description"
msgid "Footer with site title and powered by WordPress."
msgstr "Footer mit Website-Titel und „Präsentiert von WordPress“."

#: patterns/call-to-action.php
msgctxt "Pattern description"
msgid "Left-aligned text with a CTA button and a separator."
msgstr "Linksbündiger Text mit einem Button zur Handlungsaufforderung und einem Trennzeichen."

#: theme.json
msgctxt "Template part name"
msgid "Comments Template Part"
msgstr "Kommentar-Template-Teil"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "Beitrags-Meta"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "Footer"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "Header"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "Systemschrift"

#: theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: theme.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: theme.json
msgctxt "Custom template name"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Blog (Alternative)"
msgstr "Blog (alternativ)"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "Leer"

#: styles/whisper.json
msgctxt "Style variation name"
msgid "Whisper"
msgstr "Flüstern"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary to Primary Fixed"
msgstr "Tertiär zu sekundär zu primär fixiert"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary Fixed"
msgstr "Primär zu sekundär zu tertiär fixiert"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary"
msgstr "Primär zu sekundär zu tertiär"

#: styles/sherbet.json
msgctxt "Style variation name"
msgid "Sherbet"
msgstr "Brause"

#: styles/pitch.json
msgctxt "Font size name"
msgid "2X Large"
msgstr "2X groß"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "Extra groß"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Large"
msgstr "Groß"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Medium"
msgstr "Mittel"

#: styles/pitch.json
msgctxt "Font size name"
msgid "small"
msgstr "Klein"

#: styles/pitch.json
msgctxt "Space size name"
msgid "7"
msgstr "7"

#: styles/pitch.json
msgctxt "Style variation name"
msgid "Pitch"
msgstr "Pech"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Dots"
msgstr "Punkte"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Base to Primary"
msgstr "Basis zu primär"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary"
msgstr "Tertiär zu sekundär"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Secondary to Primary"
msgstr "Sekundär zu primär"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Primary to Secondary"
msgstr "Primär zu sekundär"

#: styles/pilgrimage.json
msgctxt "Style variation name"
msgid "Pilgrimage"
msgstr "Pilgerfahrt"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Gigantic"
msgstr "Gigantisch"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Huge"
msgstr "Riesig"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Normal"
msgstr "Normal"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Tiny"
msgstr "Sehr klein"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/marigold.json
msgctxt "Style variation name"
msgid "Marigold"
msgstr "Ringelblume"

#: styles/grapes.json
msgctxt "Style variation name"
msgid "Grapes"
msgstr "Trauben"

#: styles/electric.json
msgctxt "Style variation name"
msgid "Electric"
msgstr "Elektrisch"

#: styles/canary.json
msgctxt "Style variation name"
msgid "Canary"
msgstr "Kanarienvogel"

#: styles/block-out.json styles/canary.json styles/pilgrimage.json
#: styles/sherbet.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "Standardfilter"

#: styles/block-out.json
msgctxt "Style variation name"
msgid "Block out"
msgstr "Blockieren"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "Tertiär"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr "Sekundär"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Primary"
msgstr "Primär"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "Kontrast"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "Basis"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Primary to Tertiary"
msgstr "Primär zu tertiär"

#: styles/aubergine.json styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Primary"
msgstr "Tertiär zu primär"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Base to Secondary to Base"
msgstr "Basis zu sekundär zu Basis"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Secondary to Base"
msgstr "Sekundär zu Basis"

#: styles/aubergine.json
msgctxt "Style variation name"
msgid "Aubergine"
msgstr "Aubergine"

#: patterns/post-meta.php:65
msgctxt "Label for a list of post tags"
msgid "Tags:"
msgstr "Schlagwörter:"

#: patterns/post-meta.php:49
msgctxt "Preposition to show the relationship between the post and its author"
msgid "by"
msgstr "von"

#: patterns/post-meta.php:37
msgctxt "Preposition to show the relationship between the post and its categories"
msgid "in"
msgstr "in"

#: patterns/post-meta.php:29
msgctxt "Verb to explain the publication status of a post"
msgid "Posted"
msgstr "Beitrag veröffentlicht"

#: patterns/post-meta.php
msgctxt "Pattern title"
msgid "Post Meta"
msgstr "Beitrags-Meta"

#: patterns/hidden-no-results.php:10
msgctxt "Message explaining that there are no results returned from a search"
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "Leider gibt es keine Übereinstimmung mit deinen Suchbegriffen. Bitte versuche es mit anderen Stichwörtern erneut."

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "Hidden No Results Content"
msgstr "Inhalt ohne Ergebnisse verborgen"

#: patterns/hidden-comments.php:13
msgctxt "Title of comments section"
msgid "Comments"
msgstr "Kommentare"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Hidden Comments"
msgstr "Versteckte Kommentare"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgid "Search"
msgstr "Suchen"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "placeholder for search field"
msgid "Search..."
msgstr "Suchen …"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "label"
msgid "Search"
msgstr "Suche"

#: patterns/hidden-404.php:19
msgctxt "Message to convey that a webpage could not be found"
msgid "This page could not be found."
msgstr "Diese Seite konnte nicht gefunden werden."

#: patterns/hidden-404.php:13
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "Hidden 404"
msgstr "404 verborgen"

#. Translators: WordPress link.
#: patterns/footer-default.php:20
msgid "Proudly powered by %s"
msgstr "Mit Stolz präsentiert von %s"

#: patterns/footer-default.php
msgctxt "Pattern title"
msgid "Default Footer"
msgstr "Standard-Footer"

#: patterns/call-to-action.php:25
msgctxt "sample content for call to action button"
msgid "Get In Touch"
msgstr "Kontakt aufnehmen"

#: patterns/call-to-action.php:16
msgctxt "sample content for call to action"
msgid "Got any book recommendations?"
msgstr "Hast du irgendwelche Buchempfehlungen?"

#: patterns/call-to-action.php
msgctxt "Pattern title"
msgid "Call to action"
msgstr "Handlungsaufforderung"

#. Author URI of the theme
#: style.css patterns/footer-default.php:21
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://de.wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "Das WordPress-Team"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentythree"
msgstr "https://de.wordpress.org/themes/twentytwentythree/"