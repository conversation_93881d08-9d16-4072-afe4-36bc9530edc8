# **Kognetiks Chatbot** Plugin Documentation

**Kognetiks Chatbot** is a plugin that allows you to effortlessly integrate conversational chat from OpenAI, Azure, NVIDIA, Anthropic, DeepSeek, local models using open-source servers into your website, providing a powerful, AI-driven chatbot for enhanced user experience and personalized support.

Conversational AI platforms - like those from OpenAI, NVIDIA, and others - use natural language processing and machine learning algorithms to interact with users in a human-like manner. They are designed to answer questions, provide suggestions, and engage in conversations with users. This is important because it can provide assistance and support to people who need it, especially in situations where human support is not available or is limited. It can also be used to automate customer service, reduce response times, and improve customer satisfaction. Moreover, these platforms can be used in various fields such as healthcare, education, finance, and many more.

The **Kognetiks Chatbot** is powered by OpenAI, NVIDIA, Anthropic or other AI platforms, via their APIs and Models to bring artificial intelligence to life within your WordPress website.

## External Services

The **Kognetiks Chatbot** plugin relies on external AI services to provide chatbot functionality. It sends user queries and related data to a third-party AI provider for processing and response generation. By using this plugin, you agree to abide by each service's terms of service and privacy policy:

- **OpenAI**: [Terms of Use](https://platform.openai.com/terms) | [Privacy Policy](https://openai.com/policies/privacy-policy/)
- **NVIDIA**: [Terms of Use](https://www.nvidia.com/en-us/about-nvidia/nv-accounts/) | [Privacy Policy](https://www.nvidia.com/en-us/about-nvidia/privacy-policy/)
- **Anthropic**: [Terms of Service](https://www.anthropic.com/legal/consumer-terms) | [Privacy Policy](https://docs.anthropic.com/en/docs/legal-center/privacy)
- **DeepSeek**: [Terms of Use](https://chat.deepseek.com/downloads/DeepSeek%20User%20Agreement.html) | [Privacy Policy](https://chat.deepseek.com/downloads/DeepSeek%20Privacy%20Policy.html)
- **Mistral**: [Terms of Service](https://mistral.ai/terms#terms-of-service) | [Privacy Policy](https://mistral.ai/terms#privacy-policy)
- **JAN.AI**: [About](https://jan.ai/about) | [Privacy Policy](https://jan.ai/docs/privacy-policy)

**IMPORTANT**:

- This plugin requires an API key from OpenAI, NVIDIA, Anthropic, DeepSeek, Mistral to function. Without an API key, the chatbot cannot process user queries.

- Obtain API keys here:

   - [OpenAI API Keys](https://platform.openai.com/account/api-keys)
   - [Auzre API Keys](https://azure.microsoft.com/en-us/pricing/purchase-options/azure-account?icid=ai-services)
   - [NVIDIA API Keys](https://developer.nvidia.com/nim)
   - [Anthropic API Keys](https://www.anthropic.com/)
   - [DeepSeek API Keys](https://platform.deepseek.com/sign_in)
   - [Mistral API Keys](https://console.mistral.ai/api-keys)
   
- By entering your API key from the AI provider of your choice and activating the chatbot, you:

   - Consent to sending user queries and related data to the selected AI provider for processing and response generation.
   - Agree to abide by the provider's terms of service, pricing, and privacy policy.
   - Acknowledge that your data, including text submitted by users, may be transferred to and processed by the AI platform in accordance with its privacy policy.

**NOTE**: You are responsible for any fees associated with the use of the selected AI platform. Be sure to review each provider's pricing and usage policies before proceeding.

## Introducing the Sentential Context Model - BETA FEATURE OFFERING

The **Kognetiks Chatbot** plugin now includes a novel feature: the Sentential Context Model.  This new **beta feature** allows the chatbot to generate intelligent responses by leveraging your website's content - no AI platform connection required.  It's perfect for localized use or content-focused applications, this feature makes the chatbot more versatile than ever.

## What's new in Version 2.3.3

* **Local Server**: Updated support for the latest release of JAN.AI local server v0.6.8. See [JAN.AI](https://jan.ai/) for more information.
* **Bug Fixes**: Resolved minor issues and bugs identified after release of version 2.3.2.

## Past Updates

* Information about past updates can be found [here](updates/updates.md).

## Quick Start

- [Overview](support/overview.md)

- [Getting Started](support/getting-started.md)

- [Official Sites](support/official-sites.md)

- [Frequently Asked Questions](support/faqs.md)

## Sections

- [General](settings/settings.md)

- [API/ChatGPT Settings](api-chatgpt-settings/api-chatgpt-model-settings.md)

- [API/Azure OpenAI Settings](api-azure-openai-settings/api-azure-openai-model-settings.md)

- [API/NVIDIA Settings](api-nvidia-settings/api-nvidia-model-settings.md)

- [API/Anthropic Settings](api-anthropic-settings/api-anthropic-model-settings.md)

- [API/DeepSeek Settings](api-deepseek-settings/api-deepseek-model-settings.md)

- [API/Mistral Settings](api-mistral-settings/api-mistral-model-settings.md)

- [API/Local Server Settings](api-local-settings/api-local-model-settings.md)

- [Assistants](assistants/manage-assistants.md)

- [Avatars](avatars/avatars.md)

- [Appearance](appearance/appearance.md)

- [Buttons](buttons/buttons.md)

- [Knowledge Navigator](knowledge-navigator/knowledge-navigator.md)

- [Knowledge Navigator Analysis](knowledge-navigator/knowledge-navigator-analysis.md)

- [Dashboard Widget](dashboard/dashboard.md)

- [Analytics Package](analytics-package/analytics-package.md) ⚠️ **Note:** This is a premium feature.

- [Reporting](reporting/reporting.md)

- [Tools](tools/tools.md)

- [Messages](messages/messages.md)

## Beta Features

- [Enabling Beta Features](beta-features/beta-features.md)

- [API/Transformer Settings](api-transformer-settings/api-transformer-model-settings.md) **BETA FEATURE**

## Support

- [How the Kognetiks Chatbot Works](support/how-it-works.md)

- [Chatbots and Assistants](support/chatbots-and-assistants.md)

- [Conversation Logging and History](support/conversation-logging-and-history.md)

- [API Key Safety and Security](support/api-key-safety-and-security.md)

- [Diagnostics - For Developers](support/diagnostics.md)

## Demos

- Coming Soon

## Notice

While AI-powered applications strive for accuracy, they can sometimes make mistakes. We recommend that you and your users verify critical information to ensure its reliability.

## Disclaimer

WordPress, OpenAI, ChatGPT, NVIDIA, NIM, Anthropic, Claude, DeepSeek, Mistral, Azure and related trademarks are the property of their respective owners. Kognetiks is an independent entity and is not affiliated with, endorsed by, or sponsored by WordPress Foundation, OpenAI, NVIDIA, Anthropic, DeepSeek or Mistral.

---

* **[Back to the Overview](/overview.md)**
