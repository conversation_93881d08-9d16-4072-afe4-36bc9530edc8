# Kognetiks Chatbot

The **Kognetiks Chatbot** plugin project is centered around revolutionizing digital interactions on WordPress websites looking to incorporate Artificial Intelligent products such as those powered by OpenAI, Azure, NVIDIA, Anthropic, Mistral and local servers running open source AI models. The plugin is designed to enhance visitor engagement through intelligent and interactive conversational experiences, leveraging advanced AI technology for customer support and other conversational applications on WordPress sites. This project aims to make cutting-edge AI accessible and functional within the WordPress ecosystem.

**🚀 Exciting News: Local AI Support for Kognetiks Chatbot!**:

Kognetiks Chatbot now offers seamless integration with local AI servers like JAN.AI, an open-source LLM solution, bringing powerful AI capabilities to your WordPress site—without the need for costly API subscriptions. This new feature allows you to deploy AI-driven conversations while keeping full control over your data and infrastructure.

**🔑 Why Go Local?**
* **Cost-Effective** – No ongoing API fees, reducing operational expenses.
* **Privacy-Focused** – Your data stays on your own servers, ensuring security and compliance.
* **Customizable** – Choose from various open-source models tailored to your needs.
* **Fast & Responsive** – Local processing means quicker interactions and reduced latency.
* **Full Control** – Manage your AI infrastructure independently for maximum flexibility.

With Kognetiks Chatbot and JAN.AI, you can create dynamic conversational experiences while maintaining data sovereignty. Ideal for business websites, educational platforms, e-commerce sites, community forums, and knowledge bases, this integration empowers you to harness AI on your terms!

**🌟 Introducing the Sentential Context Model - BETA FEATURE OFFERING**:

The **Kognetiks Chatbot** plugin now includes a novel feature: the Sentential Context Model.  This new **beta feature** allows the chatbot to generate intelligent responses by leveraging your website's content - no AI platform connection required.  It's perfect for localized use or content-focused applications, this feature makes the chatbot more versatile than ever.

## Documentation

**What's new in Version 2.3.3**

* **Local Server**: Updated support for the latest release of JAN.AI local server v0.6.8. See [JAN.AI](https://jan.ai/) for more information.
* **Bug Fixes**: Resolved minor issues and bugs identified after release of version 2.3.2.

## Features

Welcome to the future of website interaction with **Kognetiks Chatbot**, your gateway to unparalleled visitor engagement powered by your choice of AI Platform from OpenAI, NVIDIA, and Anthropic to deliver an intelligent, conversational AI experience on your website. This plugin is ideal for businesses, educators, and bloggers who want to automate customer support, enhance user interaction, and provide personalized assistance in real-time.

**🌐 Harnessing Large Language Models (LLMs) for Enhanced Engagement**
Dive into the world of advanced AI with Large Language Models at the core of the Chatbot. These models are trained to understand and respond to user queries in a natural, conversational manner. They're not just chatbots; they're intelligent conversational partners that can engage, inform, and assist your visitors in real time. Whether it's providing detailed answers to complex queries or engaging in casual conversation, these models are equipped to elevate the user experience on your website.

**🌟 Assistants: Tailored Conversational Experiences**
Unlock the potential of personalized digital interaction. Use the Assistants you develop, trained with your specific knowledge and skills, to revolutionize your website. From handling FAQs to managing bookings and offering customized suggestions, these Assistants are seamlessly integrated into your WordPress site, promising a dynamic and engaging user experience.

**🔀 Multiple Assistants, Multiple Roles on the OpenAI platform only**
The latest version of the plugin allows for virtually unlimited Assistants, allowing you to deploy a unique assistant wherever you placed the shortcode.  Simply pass the "asst_" ID to the shortcode as one of the parameters.  Discover more about these innovative features at [Kognetiks.com](https://kognetiks.com/wordpress-plugins/kognetiks-chatbot/) and [OpenAI's Playground](https://platform.openai.com/assistants).

**🔄 Choose Your Style: Embedded or Floating Chatbots**
Flexibility is key. Display your AI-powered chatbot as an embedded feature on pages or let it float across your site. With simple shortcodes, adapt the chatbot's presence to match your website's design and user needs.

**🔍 Knowledge Navigator: Unearthing Your Content's Essence**
At the heart of the plugin lies the Knowledge Navigator. This powerful tool delves deep into your website, mapping its architecture and content, enabling the chatbot to deliver precise and contextually relevant responses. Enhanced by TF-IDF analysis, the Knowledge Navigator ensures your content's unique keywords shine through, making interactions more meaningful.

**🗎 Conversation Logging**
Conversation Logging in this plugin records and stores chat interactions between users and the chatbot, providing valuable insights for enhancing user experience and chatbot performance. Visit the privacy policy on the Settings Support tab for details on data handling.

**🎭 Personalize with Custom Avatars**
Add a creative touch with customizable avatars. Reflect your site's personality through these visual companions, enhancing user engagement and adding a unique flair to your digital space.

**📊 Direct Traffic with Customizable Buttons**
Guide your visitors where you want them. Customizable buttons can link directly to specific pages, forms, or contact information, facilitating smoother navigation and enhanced user engagement.

**🤖 Tailored Audience Engagement**
Customize accessibility with three audience settings: All Audiences, Logged-in Only, or Visitors Only. Additionally, control presentation to specific audiences. Whether floating or embedded, tailor the chatbot's visibility for a seamless user experience across platforms.

**🎭Personalized Greetings:**
Users now have the option to personalize both initial and subsequent greetings for the chatbot, enhancing the user experience with a more individualized interaction. Just add any field from your _users or _usermeta table in WordPress to the Initial Greeting or Subsequent Greeting, such as: "Hello [first_name], how can I help you today?". This can be found under Settings > Kognetiks Chatbot > Settings.

**🤖Display Assistant's Name:**
We've introduced a feature that allows the display of the Assistant's name, which is sourced directly from the OpenAI platform. This can be found and adjusted under Settings > Kognetiks Chatbot > GPT Assistants > Display GPT Assistant Name.

**🌟Support for Additional Models:**
Our support model range has been expanded to include not just text but also image and speech functionalities. This broadens the chatbot's application in various interactive scenarios. You can call the Chatbot using the "model" parameter in the shortcode.

**🌟Image Generation with DALL-E:**
The chatbot is now equipped to generate images using OpenAI's DALL-E models, offering users a new dimension of creativity and visual interaction. To generate images using the "dall-e-3" model, use the shortcode **[chatbot style=embedded model=dall-e-3]**.

**🔄Text-to-Speech Conversion:**
With the integration of Text-to-Speech (TTS) models, the chatbot can now convert text inputs into spoken word, making it accessible for auditory communication and enhancing user engagement through speech. To generate speech from text, use the shortcode **[chatbot style=embedded model=tts-1-1106]**.

**🤖 Why the Kognetiks Chatbot?**
▪ **Natural Conversations:** Experience human-like interactions, thanks to Large Language Model APIs from companies like OpenAI.
▪ **Always Available:** Provide round-the-clock assistance in various domains, from healthcare to education.
▪ **Seamless Integration:** Effortlessly bring your WordPress site to life with an easy-to-use plugin.

**✨ Supported Models from OpenAI, NVIDIA, Anthropic, DeepSeek, and Mistral**

For a full list of models see:

- [OpenAI's Model Overview](https://platform.openai.com/docs/models/overview)
- [NVIDIA's Model Overview](https://docs.nvidia.com/nim/large-language-models/latest/models.html)
- [Anthropic's Model Overview](https://docs.anthropic.com/en/docs/about-claude/models)
- [DeepSeek's Model Overview](https://api-docs.deepseek.com/quick_start/pricing)
- [Mistral's Model Overview](https://docs.mistral.ai/getting-started/models/models_overview/)
- [JAN.AI's Model Overview](https://jan.ai/docs/models/manage-models#add-models)

For a full list of Deep

**🚀 Elevate Your Website Experience**
The Kognetiks Chatbot is more than just a plugin – it's a transformational tool for your website. With advanced AI technology at its core, it promises a unique and interactive experience for your visitors.

Get your Kognetiks Chatbot today and redefine your WordPress site with intelligence and a personal touch.

## External Services

The **Kognetiks Chatbot** plugin relies on external AI services to provide chatbot functionality. It sends user queries and related data to a third-party AI provider for processing and response generation. By using this plugin, you agree to abide by each service’s terms of service and privacy policy:

- **OpenAI**: [Terms of Use](https://platform.openai.com/terms) | [Privacy Policy](https://openai.com/policies/privacy-policy/)
- **Azure OpenAI**: [Terms of Use](https://learn.microsoft.com/en-us/legal/cognitive-services/openai/data-privacy?tabs=azure-portal) | [Privacy Polisy](https://www.microsoft.com/en-us/privacy/privacystatement)
- **NVIDIA**: [Terms of Use](https://www.nvidia.com/en-us/about-nvidia/nv-accounts/) | [Privacy Policy](https://www.nvidia.com/en-us/about-nvidia/privacy-policy/)
- **Anthropic**: [Terms of Service](https://www.anthropic.com/legal/consumer-terms) | [Privacy Policy](https://docs.anthropic.com/en/docs/legal-center/privacy)
- **DeepSeek**: [Terms of Use](https://chat.deepseek.com/downloads/DeepSeek%20User%20Agreement.html) | [Privacy Policy](https://chat.deepseek.com/downloads/DeepSeek%20Privacy%20Policy.html)
- **Mistral**: [Terms of Service](https://mistral.ai/terms#terms-of-service) | [Privacy Policy](https://mistral.ai/terms#privacy-policy)
- **JAN.AI**: [About](https://jan.ai/about) | [Privacy Policy](https://jan.ai/docs/privacy-policy)

**IMPORTANT**:

- This plugin requires an API key from OpenAI, NVIDIA, Anthropic, DeepSeek or Mistral to function. Without an API key, the chatbot cannot process user queries.

- Obtain API keys here:

   - [OpenAI API Keys](https://platform.openai.com/account/api-keys)
   - [Auzre API Keys](https://azure.microsoft.com/en-us/pricing/purchase-options/azure-account?icid=ai-services)
   - [NVIDIA API Keys](https://developer.nvidia.com/nim)
   - [Anthropic API Keys](https://www.anthropic.com/)
   - [DeepSeek API Keys](https://platform.deepseek.com/sign_in)
   - [Mistral API Keys](https://console.mistral.ai/api-keys)

- By entering your API key from the AI provider of your choice and activating the chatbot, you:

   - Consent to sending user queries and related data to the selected AI provider for processing and response generation.
   - Agree to abide by the provider’s terms of service, pricing, and privacy policy.
   - Acknowledge that your data, including text submitted by users, may be transferred to and processed by the AI platform in accordance with its privacy policy.

**NOTE**: You are responsible for any fees associated with the use of the selected AI platform. Be sure to review each provider’s pricing and usage policies before proceeding.

## 🌐 Features at a Glance

* **Quick Setup:** Integrate easily with API from companies like OpenAI.
* **Advanced AI Models:** Includes support for the latest LLMs from OpenAI, NVIDIA, and Anthropic.
* **Customizable Interfaces:** Choose between floating and embedded chatbot styles.
* **User-Friendly Settings:** Easily manage your API key and other settings.
* **Intelligent Design:** Smart collapsible chatbot for a cleaner website interface.
* **Engaging User Interaction:** Customize greetings and messages for a unique visitor experience.
* **Persistent Memory:** The chatbot remembers interactions, offering continuity across pages.
* **In-depth Content Analysis:** Knowledge Navigator ensures contextually relevant interactions.

## Getting Started

1. Obtain your API key by signing up with the AI Platform vendor of your choice..
2. Install and activate the Chatbot plugin.
3. Navigate to the settings page (Settings > API/Model) and enter your API key.
4. Customize the chatbot appearance and other parameters as needed.
5. For a floating chatbot add the shortcode to your theme's footer: `[chatbot]` or `[chatbotstyle=floating]`
6. For an embedded chatbot on any page add the shortcode: `[chatbot style=embedded]`
7. Use `[chatbot style=floating|embedded assistant=primary|alternate]` to display the chatbot as a floating chatbot or embedded chatbot with a primary or alternate assistant.

Now your website visitors can enjoy a seamless and personalized chat experience with the Kognetiks Chatbot.

## Installing the Chatbot on Your WordPress Website

Embark on a journey to elevate your website's interactivity with this Chatbot plugin. Here's how to get started:

1. **Plugin Upload**
   - Begin by downloading the 'chatbot-chatgpt' plugin folder.
   - Navigate to your WordPress website's dashboard.
   - Click on 'Plugins' and select 'Add New'.
   - Choose the 'Upload Plugin' option at the top of the page.
   - Upload the 'chatbot-chatgpt' folder and click 'Install Now'.

2. **Plugin Activation**
   - Once the installation is complete, activate the plugin by clicking 'Activate Plugin'.

3. **API Key Configuration**
   - After activation, head to 'Settings > General' in your dashboard.
   - Enter the API key you generate with the AI Platform of your choice.

4. **Customizing Your Chatbot**
   - In the same settings area, tailor the chatbot's appearance and functionality to match your site's style and your specific needs.

5. **Embedding the Chatbot**
   - You can add the chatbot to any page, footer, or sidebar of your theme.
   - Use the shortcode `[chatbot]` for a standard chatbot.
   - For a floating chatbot, use `[chatbot style=floating]`.
   - If you prefer an embedded chatbot, use `[chatbot style=embedded]`.

6. **Knowledge Navigator Setup**
   - To fully utilize the capabilities of Chatbot, go back to 'Settings > Chatbot' and click on the 'Knowledge Navigator' tab.
   - Initiate a site scan to allow the Knowledge Navigator to map and understand your site's content.

7. **Scheduling Knowledge Navigator**
   - Opt for hourly, daily, or weekly scans through the Knowledge Navigator to ensure the chatbot stays updated with your latest content.

## Your Journey Towards an Interactive Website Begins!

With the Kognetiks Chatbot installed, you're now equipped to offer a more dynamic, engaging, and responsive experience to your website visitors.

## Frequently Asked Questions

**How do I obtain an API key for the API?**

To obtain an API key, sign up for an account at [https://platform.openai.com/account/api-keys](https://platform.openai.com/account/api-keys), [NVIDIA API Keys](https://developer.nvidia.com/nim), [Anthropic API Keys](https://www.anthropic.com/), [DeepSeek API Keys](https://platform.deepseek.com/sign_in), or [Mistral API Keys](https://console.mistral.ai/api-keys). Once registered, you may create your API key(s).

**Can I customize the appearance of the chatbot?**

Yes, the plugin comes with a default style, but you can easily customize the chatbot's appearance by editing the chatbot-chatgpt.css file or adding custom CSS rules to your WordPress theme.

You can also customize the name of the chatbot, as well as changing the initial greeting and subsequent greeting.

**Is the chatbot available in multiple languages?**

Yes, the Kognetiks Chatbot and the OpenAI's ChatGPT API support many different languages. Set the 'Site Language' option in WordPress to your preference.

**Which OpenAI models does the plugin use?**

The plugin supports the gpt-3.5-turbo, gpt-4, gpt-4-1106-preview models from OpenAI.  These are the same models found in the ChatGPT product from OpenAI.

The plugin now supports the latest OpenAI model **gpt-4-turbo (i.e., 'gpt-4-1106-preview')** featuring improved instruction following based on training data up to April 2023.  New models will be added as the become available.

## API Key Safety and Security

Your API key serves as the confidential password providing access to your OpenAI account and the resources associated with it. If this key falls into the wrong hands, it can be misused in a variety of detrimental ways, including unauthorized usage, potential data leaks, and the improper application of AI models. It's crucial, therefore, to implement the following protective measures:

1. Secure key storage: Ensure your API keys are stored in a safe and secure manner.
2. Monitor and review usage: Frequently scrutinize and evaluate the usage of your API key.
3. Establish usage limits: Initially, implement a low hard limit to ensure that if the limit is reached at any point during the month, any further requests will be denied.
4. Regular key rotation: Frequently changing your API keys can reduce the risk of misuse. If you observe any unexpected activity, it's important to immediately revoke your API keys.
5. As a preventative measure, you might want to regularly revoke them to avert misuse.

Remember, wielding AI power requires immense responsibility — it's incumbent upon us all to ensure its careful and secure use.

## License

- License: GPLv3 or later
- License URI: https://www.gnu.org/licenses/gpl-3.0.html

## Support

💬 Looking for **plugin support**, please visit [https://kognetiks.com/plugin-support/](https://kognetiks.com/plugin-support/).

## Disclaimer

WordPress is a registered trademark of the WordPress Foundation. OpenAI, ChatGPT, and related trademarks are the property of OpenAI. NVIDIA, NIM, and related trademarks are the property of NVIDIA. Anthropic, Claude, and related trademarks are the property of Anthropic. DeepSeek and related trademarks are the property of DeepSeek. Mistral and related trademarks are the property of Mistral. Kognetiks is an independent entity and is not affiliated with, endorsed by, or sponsored by WordPress Foundation, OpenAI, NVIDIA, Anthropic, DeepSeek or Mistral.

## Thank you for using Kognetiks Chatbot

Visit us at [Kognetiks.com](https://kognetiks.com/wordpress-plugins/kognetiks-chatbot/ai-powered-chatbot-for-wordpress/) for more information.
