# Security Policy

## Supported Versions

| Version   | Supported          |
|-----------|--------------------|
|   2.3.3   | :white_check_mark: |
| < 2.3.3   | :x:                |

## Reporting a Vulnerability

To report a vulnerability in our plugin, please follow these steps:

1. **Email the Support Team**: Send an email to [<EMAIL>](mailto:<EMAIL>) with the details of the vulnerability.

2. **Information to Include**: Provide a detailed description of the issue, including:

    - Steps to reproduce
    - Potential impact
    - Any relevant code snippets or screenshots

3. **Response Time**: You can expect an initial response within 72 hours of your report. We will endeavor to keep you informed of the progress and any actions taken.

4. **Handling Process**:

   - If the vulnerability is confirmed, we will work on a fix and release an update. You may be credited for your report unless you wish to remain anonymous.
   
   - If the vulnerability is not accepted, we will provide an explanation and any relevant context.

**Confidentiality**: We ask that you do not share details of the vulnerability publicly until it has been addressed.

**Feedback**: If you have any feedback on the reporting process or our security policy, feel free to include it in your report.

Thank you for helping us keep our plugin secure!
