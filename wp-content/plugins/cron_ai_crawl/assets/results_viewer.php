<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sodexo AI Crawler Results</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .filters {
            padding: 20px 30px;
            background: white;
            border-bottom: 1px solid #eee;
        }

        .filter-group {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-group label {
            font-weight: 600;
            color: #2c3e50;
        }

        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .filter-group select:focus, .filter-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .add-partner-form {
            background: #f8f9fa;
            padding: 25px;
            margin: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }

        .add-partner-form h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .form-group input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }

        .table-container {
            overflow-x: auto;
            padding: 0 30px 30px;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .results-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .results-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: top;
        }

        .results-table tr:hover {
            background-color: #f8f9fa;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
        }

        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }

        .company-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .company-url {
            font-size: 0.85rem;
            color: #667eea;
            text-decoration: none;
        }

        .company-url:hover {
            text-decoration: underline;
        }

        .content-preview {
            max-width: 300px;
            max-height: 100px;
            overflow: hidden;
            font-size: 0.85rem;
            line-height: 1.4;
            color: #6c757d;
        }

        .response-time {
            font-weight: 600;
            color: #28a745;
        }

        .error-message {
            color: #dc3545;
            font-size: 0.85rem;
            font-style: italic;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
                padding: 20px;
            }
            
            .filter-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .results-table {
                font-size: 0.85rem;
            }
            
            .results-table th,
            .results-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Sodexo AI Crawler Results</h1>
            <p>Partner Website Analysis & Business Intelligence</p>
        </div>
        <div><a href="https://nl-hh.eu/index.php/crawling-ergebnisse/">Zurück zur Webseite</a></div>
        <?php
        // Include database connection
        require_once 'class_sql_executor.php';

        // Database connection
        $db = new SQL_Executor(
            'localhost',
            'wp_sodexo_juni25',
            'ydGG8U5V_kjsd7NAc8F',
            'wp_sodexo_juni25',
            3306
        );

        // Handle form submission for adding new partner
        $message = '';
        $messageType = '';

        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_partner'])) {
            $partnerName = trim($_POST['partner_name'] ?? '');
            $partnerUrl = trim($_POST['partner_url'] ?? '');

            // Validation
            if (empty($partnerName)) {
                $message = 'Partner Name ist erforderlich.';
                $messageType = 'error';
            } elseif (empty($partnerUrl)) {
                $message = 'Partner URL ist erforderlich.';
                $messageType = 'error';
            } elseif (!filter_var($partnerUrl, FILTER_VALIDATE_URL)) {
                $message = 'Bitte geben Sie eine gültige URL ein.';
                $messageType = 'error';
            } else {
                // Check if partner already exists
                $checkQuery = "SELECT id FROM wp_sodexo_partners WHERE name = ? OR url = ?";
                $existingPartner = $db->select($checkQuery, [$partnerName, $partnerUrl]);

                if ($existingPartner) {
                    $message = 'Ein Partner mit diesem Namen oder dieser URL existiert bereits.';
                    $messageType = 'error';
                } else {
                    // Insert new partner
                    $insertQuery = "INSERT INTO wp_sodexo_partners (name, url, created_at) VALUES (?, ?, NOW())";
                    $result = $db->insert($insertQuery, [$partnerName, $partnerUrl]);

                    if ($result) {
                        $message = 'Partner "' . htmlspecialchars($partnerName) . '" wurde erfolgreich hinzugefügt!';
                        $messageType = 'success';
                        // Clear form data
                        $_POST = [];
                    } else {
                        $message = 'Fehler beim Hinzufügen des Partners: ' . $db->get_last_error();
                        $messageType = 'error';
                    }
                }
            }
        }
        ?>

        <!-- Add Partner Form -->
        <div class="add-partner-form">
            <h2>🏢 Neuen Partner hinzufügen</h2>

            <?php if ($message): ?>
                <div class="<?= $messageType ?>-message">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="">
                <div class="form-row">
                    <div class="form-group">
                        <label for="partner_name">Partner Name *</label>
                        <input type="text"
                               id="partner_name"
                               name="partner_name"
                               value="<?= htmlspecialchars($_POST['partner_name'] ?? '') ?>"
                               placeholder="z.B. Musterfirma GmbH"
                               required>
                    </div>
                    <div class="form-group">
                        <label for="partner_url">Partner URL *</label>
                        <input type="url"
                               id="partner_url"
                               name="partner_url"
                               value="<?= htmlspecialchars($_POST['partner_url'] ?? '') ?>"
                               placeholder="https://www.beispiel.de"
                               required>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" name="add_partner" class="btn btn-primary">
                        ➕ Partner hinzufügen
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        🔄 Formular zurücksetzen
                    </button>
                </div>
            </form>
        </div>

        // Get summary statistics
        $summaryQuery = "
            SELECT 
                COUNT(*) as total_partners,
                SUM(CASE WHEN crawl_status = 'success' THEN 1 ELSE 0 END) as successful_crawls,
                SUM(CASE WHEN crawl_status = 'failed' THEN 1 ELSE 0 END) as failed_crawls,
                AVG(CASE WHEN crawl_status = 'success' THEN response_time_ms ELSE NULL END) as avg_response_time,
                MAX(crawl_timestamp) as last_crawl
            FROM wp_crawl_results
        ";

        $summary = $db->select($summaryQuery);
        $stats = $summary ? $summary[0] : null;

        if ($stats): ?>
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?= $stats['total_partners'] ?></div>
                <div class="stat-label">Total Partners</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $stats['successful_crawls'] ?></div>
                <div class="stat-label">Successful Crawls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= $stats['failed_crawls'] ?></div>
                <div class="stat-label">Failed Crawls</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= number_format($stats['avg_response_time'], 0) ?>ms</div>
                <div class="stat-label">Avg Response Time</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?= round(($stats['successful_crawls'] / $stats['total_partners']) * 100, 1) ?>%</div>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>
        <?php endif; ?>

        <div class="filters">
            <div class="filter-group">
                <label for="statusFilter">Filter by Status:</label>
                <select id="statusFilter" onchange="filterTable()">
                    <option value="">All Statuses</option>
                    <option value="success">Success</option>
                    <option value="failed">Failed</option>
                </select>

                <label for="searchFilter">Search:</label>
                <input type="text" id="searchFilter" placeholder="Search company names..." onkeyup="filterTable()">
            </div>
        </div>

        <div class="table-container">
            <?php
            // Get all crawl results
            $resultsQuery = "
                SELECT 
                    id,
                    partner_name,
                    partner_url,
                    crawl_status,
                    response_time_ms,
                    LEFT(facility_services_found, 200) as services_preview,
                    LEFT(contact_info_found, 150) as contact_preview,
                    LEFT(business_opportunities, 200) as opportunities_preview,
                    error_message,
                    crawl_timestamp
                FROM wp_crawl_results 
                ORDER BY crawl_timestamp DESC
            ";

            $results = $db->select($resultsQuery);

            if ($results): ?>
            <table class="results-table" id="resultsTable">
                <thead>
                    <tr>
                        <th>Company</th>
                        <th>Status</th>
                        <th>Services Found</th>
                        <th>Contact Info</th>
                        <th>Opportunities</th>
                        <th>Response Time</th>
                        <th>Crawled</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($results as $result): ?>
                    <tr data-status="<?= $result['crawl_status'] ?>" data-company="<?= strtolower($result['partner_name']) ?>">
                        <td>
                            <div class="company-name"><?= htmlspecialchars($result['partner_name']) ?></div>
                            <a href="<?= htmlspecialchars($result['partner_url']) ?>" target="_blank" class="company-url">
                                <?= htmlspecialchars($result['partner_url']) ?>
                            </a>
                        </td>
                        <td>
                            <span class="status-badge status-<?= $result['crawl_status'] ?>">
                                <?= ucfirst($result['crawl_status']) ?>
                            </span>
                            <?php if ($result['crawl_status'] === 'failed' && $result['error_message']): ?>
                                <div class="error-message"><?= htmlspecialchars($result['error_message']) ?></div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="content-preview">
                                <?= $result['services_preview'] ? htmlspecialchars($result['services_preview']) . '...' : 'No services identified' ?>
                            </div>
                        </td>
                        <td>
                            <div class="content-preview">
                                <?= $result['contact_preview'] ? htmlspecialchars($result['contact_preview']) . '...' : 'No contact info found' ?>
                            </div>
                        </td>
                        <td>
                            <div class="content-preview">
                                <?= $result['opportunities_preview'] ? htmlspecialchars($result['opportunities_preview']) . '...' : 'No opportunities identified' ?>
                            </div>
                        </td>
                        <td>
                            <span class="response-time"><?= number_format($result['response_time_ms']) ?>ms</span>
                        </td>
                        <td>
                            <?= date('M j, Y H:i', strtotime($result['crawl_timestamp'])) ?>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="loading">
                <h3>No crawl results found</h3>
                <p>Run the tender_crawler.php script to generate data.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function filterTable() {
            const statusFilter = document.getElementById('statusFilter').value.toLowerCase();
            const searchFilter = document.getElementById('searchFilter').value.toLowerCase();
            const table = document.getElementById('resultsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const status = row.getAttribute('data-status');
                const company = row.getAttribute('data-company');
                
                let showRow = true;

                // Status filter
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }

                // Search filter
                if (searchFilter && !company.includes(searchFilter)) {
                    showRow = false;
                }

                row.style.display = showRow ? '' : 'none';
            }
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
