<?php
class SQL_Executor {
    private $connection;
    private $last_error;

    public function __construct($host, $username, $password, $database, $port = 3306) {
        try {
            $this->connection = new mysqli($host, $username, $password, $database, $port);
            if ($this->connection->connect_error) {
                $this->last_error = $this->connection->connect_error;
            } else {
                $this->connection->set_charset('utf8mb4');
            }
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
        }
    }

    public function get_last_error() {
        return $this->last_error;
    }

    public function escape_string($string) {
        if ($this->connection) {
            return $this->connection->real_escape_string($string);
        }
        return addslashes($string);
    }

    public function query($query) {
        try {
            $result = $this->connection->query($query);
            if ($result === false) {
                $this->last_error = $this->connection->error;
                return false;
            }
            return $result;
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }

    public function select($query, $params = []) {
        try {
            if (!empty($params)) {
                $stmt = $this->connection->prepare($query);
                if ($stmt === false) {
                    $this->last_error = $this->connection->error;
                    return false;
                }

                $stmt->bind_param(str_repeat('s', count($params)), ...$params);
                $stmt->execute();
                $result = $stmt->get_result();
            } else {
                $result = $this->connection->query($query);
            }

            if ($result === false) {
                $this->last_error = $this->connection->error;
                return false;
            }

            return $result->fetch_all(MYSQLI_ASSOC);
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }

    public function update($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            if ($stmt === false) {
                $this->last_error = $this->connection->error;
                return false;
            }

            if (!empty($params)) {
                $stmt->bind_param(str_repeat('s', count($params)), ...$params);
            }

            $result = $stmt->execute();

            if ($result === false) {
                $this->last_error = $stmt->error;
                return false;
            }

            return true;
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }

    public function insert($query, $params = []) {
        try {
            $stmt = $this->connection->prepare($query);
            if ($stmt === false) {
                $this->last_error = $this->connection->error;
                return false;
            }

            if (!empty($params)) {
                $stmt->bind_param(str_repeat('s', count($params)), ...$params);
            }

            $result = $stmt->execute();

            if ($result === false) {
                $this->last_error = $stmt->error;
                return false;
            }

            return $this->connection->insert_id;
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }

    public function record_exists($table, $column, $value) {
        try {
            $query = "SELECT 1 FROM $table WHERE $column = ? LIMIT 1";
            $stmt = $this->connection->prepare($query);
            if ($stmt === false) {
                $this->last_error = $this->connection->error;
                return false;
            }

            $stmt->bind_param('s', $value);
            $stmt->execute();
            $result = $stmt->get_result();

            return $result->num_rows > 0;
        } catch (Exception $e) {
            $this->last_error = $e->getMessage();
            return false;
        }
    }

    public function __destruct() {
        if ($this->connection) {
            $this->connection->close();
        }
    }
}
