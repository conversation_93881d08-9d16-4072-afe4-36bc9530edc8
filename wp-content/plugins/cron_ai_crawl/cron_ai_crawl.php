<?php
/*
Plugin Name: Sodexo AI Crawler
Plugin URI: https://nl-hh.eu/
Description: AI-powered web crawler for analyzing partner websites using Mistral AI. Crawls URLs from partner database and provides business intelligence analysis.
Version: 1.0.0
Author: Sodexo Development Team
Author URI: https://nl-hh.eu/
License: GPL-2.0+
License URI: http://www.gnu.org/licenses/gpl-2.0.txt
Text Domain: sodexo-ai-crawler
Domain Path: /languages
Requires at least: 5.0
Tested up to: 6.8.2
Requires PHP: 7.4
Network: false

@package Sodexo_AI_Crawler
@since 1.0.0
*/

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	wp_die( 'Direct access not allowed.' );
}

// Define plugin constants
define( 'SODEXO_AI_CRAWLER_VERSION', '1.0.0' );
define( 'SODEXO_AI_CRAWLER_PLUGIN_NAME', 'sodexo-ai-crawler' );
define( 'SODEXO_AI_CRAWLER_PATH', plugin_dir_path( __FILE__ ) );
define( 'SODEXO_AI_CRAWLER_URL', plugin_dir_url( __FILE__ ) );
define( 'SODEXO_AI_CRAWLER_ASSETS_PATH', SODEXO_AI_CRAWLER_PATH . 'assets/' );
define( 'SODEXO_AI_CRAWLER_ASSETS_URL', SODEXO_AI_CRAWLER_URL . 'assets/' );

// Create upload directories for crawler data
$upload_dir = wp_upload_dir();
$crawler_temp = $upload_dir['basedir'] . '/sodexo-crawler-temp/';
$crawler_logs = $upload_dir['basedir'] . '/sodexo-crawler-logs/';

define( 'SODEXO_AI_CRAWLER_TEMP', $crawler_temp );
define( 'SODEXO_AI_CRAWLER_LOGS', $crawler_logs );

/**
 * Main plugin class
 */
class Sodexo_AI_Crawler {

    /**
     * Initialize the plugin
     */
    public function __construct() {
        add_action( 'init', array( $this, 'init' ) );
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        add_action( 'admin_init', array( $this, 'init_settings' ) );
        add_action( 'wp_ajax_start_crawler', array( $this, 'ajax_start_crawler' ) );
        add_action( 'admin_enqueue_scripts', array( $this, 'enqueue_admin_scripts' ) );

        // Include the Mistral AI cron functionality
        if ( file_exists( SODEXO_AI_CRAWLER_PATH . 'mistral-ai-cron.php' ) ) {
            require_once SODEXO_AI_CRAWLER_PATH . 'mistral-ai-cron.php';
        }
    }

    /**
     * Initialize plugin functionality
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain( 'sodexo-ai-crawler', false, dirname( plugin_basename( __FILE__ ) ) . '/languages/' );

        // Create necessary directories
        $this->create_directories();
    }

    /**
     * Create necessary directories
     */
    private function create_directories() {
        $dirs = array(
            SODEXO_AI_CRAWLER_TEMP,
            SODEXO_AI_CRAWLER_LOGS
        );

        foreach ( $dirs as $dir ) {
            if ( ! file_exists( $dir ) ) {
                wp_mkdir_p( $dir );
                // Add index.php for security
                file_put_contents( $dir . 'index.php', '<?php // Silence is golden' );
            }
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_management_page(
            __( 'Sodexo AI Crawler', 'sodexo-ai-crawler' ),
            __( 'AI Crawler', 'sodexo-ai-crawler' ),
            'manage_options',
            'sodexo-ai-crawler',
            array( $this, 'admin_page' )
        );

        // Add settings submenu
        add_submenu_page(
            'tools.php',
            __( 'AI Crawler Settings', 'sodexo-ai-crawler' ),
            __( 'Crawler Settings', 'sodexo-ai-crawler' ),
            'manage_options',
            'sodexo-ai-crawler-settings',
            array( $this, 'settings_page' )
        );
    }

    /**
     * Admin page content
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php _e( 'Sodexo AI Crawler', 'sodexo-ai-crawler' ); ?></h1>
            <p><?php _e( 'AI-powered web crawler for analyzing partner websites using Mistral AI.', 'sodexo-ai-crawler' ); ?></p>

            <div class="card">
                <h2><?php _e( 'Crawler Tools', 'sodexo-ai-crawler' ); ?></h2>
                <p><strong><?php _e( 'Partner Crawler:', 'sodexo-ai-crawler' ); ?></strong>
                   <?php printf( __( 'Run the crawler script: %s', 'sodexo-ai-crawler' ),
                         '<code>php ' . SODEXO_AI_CRAWLER_ASSETS_PATH . 'tender_crawler.php</code>' ); ?>
                </p>
                <p><strong><?php _e( 'Check Results:', 'sodexo-ai-crawler' ); ?></strong>
                   <?php printf( __( 'View results: %s', 'sodexo-ai-crawler' ),
                         '<code>php ' . SODEXO_AI_CRAWLER_ASSETS_PATH . 'detailed_results.php</code>' ); ?>
                </p>
            </div>

            <div class="card">
                <h2><?php _e( 'Database Tables', 'sodexo-ai-crawler' ); ?></h2>
                <p><?php _e( 'The plugin uses the following database tables:', 'sodexo-ai-crawler' ); ?></p>
                <ul>
                    <li><code>wp_sodexo_partners</code> - <?php _e( 'Partner information and URLs', 'sodexo-ai-crawler' ); ?></li>
                    <li><code>wp_crawl_results</code> - <?php _e( 'Crawling results and AI analysis', 'sodexo-ai-crawler' ); ?></li>
                    <li><code>wp_mistral_ai_logs</code> - <?php _e( 'Mistral AI API logs', 'sodexo-ai-crawler' ); ?></li>
                </ul>
            </div>
        </div>
        <?php
    }

    /**
     * Initialize settings
     */
    public function init_settings() {
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_mistral_ai_api_key' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_mistral_ai_model' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_mistral_ai_temperature' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_mistral_ai_max_tokens' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_enabled' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_interval' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_timeout' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_user_agent' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_log_level' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_log_retention_days' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_enable_debug' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_batch_size' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_max_retries' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_allowed_domains' );
        register_setting( 'sodexo_ai_crawler_settings', 'sodexo_crawler_rate_limit' );
    }

    /**
     * Settings page content
     */
    public function settings_page() {
        if ( isset( $_POST['submit'] ) ) {
            // Handle form submission
            update_option( 'sodexo_mistral_ai_api_key', sanitize_text_field( $_POST['sodexo_mistral_ai_api_key'] ) );
            update_option( 'sodexo_mistral_ai_model', sanitize_text_field( $_POST['sodexo_mistral_ai_model'] ) );
            update_option( 'sodexo_mistral_ai_temperature', floatval( $_POST['sodexo_mistral_ai_temperature'] ) );
            update_option( 'sodexo_mistral_ai_max_tokens', intval( $_POST['sodexo_mistral_ai_max_tokens'] ) );
            update_option( 'sodexo_crawler_enabled', isset( $_POST['sodexo_crawler_enabled'] ) ? 1 : 0 );
            update_option( 'sodexo_crawler_interval', sanitize_text_field( $_POST['sodexo_crawler_interval'] ) );
            update_option( 'sodexo_crawler_timeout', intval( $_POST['sodexo_crawler_timeout'] ) );
            update_option( 'sodexo_crawler_user_agent', sanitize_text_field( $_POST['sodexo_crawler_user_agent'] ) );
            update_option( 'sodexo_crawler_log_level', sanitize_text_field( $_POST['sodexo_crawler_log_level'] ) );
            update_option( 'sodexo_crawler_log_retention_days', intval( $_POST['sodexo_crawler_log_retention_days'] ) );
            update_option( 'sodexo_crawler_enable_debug', isset( $_POST['sodexo_crawler_enable_debug'] ) ? 1 : 0 );
            update_option( 'sodexo_crawler_batch_size', intval( $_POST['sodexo_crawler_batch_size'] ) );
            update_option( 'sodexo_crawler_max_retries', intval( $_POST['sodexo_crawler_max_retries'] ) );
            update_option( 'sodexo_crawler_allowed_domains', sanitize_textarea_field( $_POST['sodexo_crawler_allowed_domains'] ) );
            update_option( 'sodexo_crawler_rate_limit', intval( $_POST['sodexo_crawler_rate_limit'] ) );

            echo '<div class="updated"><p>' . __( 'Settings saved successfully!', 'sodexo-ai-crawler' ) . '</p></div>';
        }

        // Get current values (with defaults from wp-config.php constants)
        $api_key = get_option( 'sodexo_mistral_ai_api_key', defined( 'MISTRAL_AI_API_KEY' ) ? MISTRAL_AI_API_KEY : '' );
        $model = get_option( 'sodexo_mistral_ai_model', defined( 'MISTRAL_AI_MODEL' ) ? MISTRAL_AI_MODEL : 'mistral-tiny' );
        $temperature = get_option( 'sodexo_mistral_ai_temperature', defined( 'MISTRAL_AI_TEMPERATURE' ) ? MISTRAL_AI_TEMPERATURE : 0.7 );
        $max_tokens = get_option( 'sodexo_mistral_ai_max_tokens', defined( 'MISTRAL_AI_MAX_TOKENS' ) ? MISTRAL_AI_MAX_TOKENS : 2000 );
        $crawler_enabled = get_option( 'sodexo_crawler_enabled', defined( 'SODEXO_CRAWLER_ENABLED' ) ? SODEXO_CRAWLER_ENABLED : true );
        $crawler_interval = get_option( 'sodexo_crawler_interval', defined( 'SODEXO_CRAWLER_INTERVAL' ) ? SODEXO_CRAWLER_INTERVAL : 'hourly' );
        $crawler_timeout = get_option( 'sodexo_crawler_timeout', defined( 'SODEXO_CRAWLER_TIMEOUT' ) ? SODEXO_CRAWLER_TIMEOUT : 30 );
        $user_agent = get_option( 'sodexo_crawler_user_agent', defined( 'SODEXO_CRAWLER_USER_AGENT' ) ? SODEXO_CRAWLER_USER_AGENT : 'Sodexo AI Crawler/1.0' );
        $log_level = get_option( 'sodexo_crawler_log_level', defined( 'SODEXO_CRAWLER_LOG_LEVEL' ) ? SODEXO_CRAWLER_LOG_LEVEL : 'info' );
        $log_retention = get_option( 'sodexo_crawler_log_retention_days', defined( 'SODEXO_CRAWLER_LOG_RETENTION_DAYS' ) ? SODEXO_CRAWLER_LOG_RETENTION_DAYS : 30 );
        $enable_debug = get_option( 'sodexo_crawler_enable_debug', defined( 'SODEXO_CRAWLER_ENABLE_DEBUG' ) ? SODEXO_CRAWLER_ENABLE_DEBUG : false );
        $batch_size = get_option( 'sodexo_crawler_batch_size', defined( 'SODEXO_CRAWLER_BATCH_SIZE' ) ? SODEXO_CRAWLER_BATCH_SIZE : 10 );
        $max_retries = get_option( 'sodexo_crawler_max_retries', defined( 'SODEXO_CRAWLER_MAX_RETRIES' ) ? SODEXO_CRAWLER_MAX_RETRIES : 3 );
        $allowed_domains = get_option( 'sodexo_crawler_allowed_domains', defined( 'SODEXO_CRAWLER_ALLOWED_DOMAINS' ) ? SODEXO_CRAWLER_ALLOWED_DOMAINS : 'sodexo.com,partner-sites.com' );
        $rate_limit = get_option( 'sodexo_crawler_rate_limit', defined( 'SODEXO_CRAWLER_RATE_LIMIT' ) ? SODEXO_CRAWLER_RATE_LIMIT : 5 );
        ?>
        <div class="wrap">
            <h1><?php _e( 'Sodexo AI Crawler Settings', 'sodexo-ai-crawler' ); ?></h1>
            <p><?php _e( 'Configure the AI crawler settings below. These settings override the defaults defined in wp-config.php.', 'sodexo-ai-crawler' ); ?></p>

            <form method="post" action="">
                <?php wp_nonce_field( 'sodexo_ai_crawler_settings' ); ?>

                <div class="card">
                    <h2><?php _e( 'Mistral AI Configuration', 'sodexo-ai-crawler' ); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="sodexo_mistral_ai_api_key"><?php _e( 'API Key', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="password" id="sodexo_mistral_ai_api_key" name="sodexo_mistral_ai_api_key"
                                       value="<?php echo esc_attr( $api_key ); ?>" class="regular-text" />
                                <p class="description"><?php _e( 'Your Mistral AI API key for making AI requests.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_mistral_ai_model"><?php _e( 'AI Model', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <select id="sodexo_mistral_ai_model" name="sodexo_mistral_ai_model">
                                    <option value="mistral-tiny" <?php selected( $model, 'mistral-tiny' ); ?>>Mistral Tiny</option>
                                    <option value="mistral-small" <?php selected( $model, 'mistral-small' ); ?>>Mistral Small</option>
                                    <option value="mistral-medium" <?php selected( $model, 'mistral-medium' ); ?>>Mistral Medium</option>
                                    <option value="mistral-large" <?php selected( $model, 'mistral-large' ); ?>>Mistral Large</option>
                                </select>
                                <p class="description"><?php _e( 'The Mistral AI model to use for analysis.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_mistral_ai_temperature"><?php _e( 'Temperature', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_mistral_ai_temperature" name="sodexo_mistral_ai_temperature"
                                       value="<?php echo esc_attr( $temperature ); ?>" min="0" max="1" step="0.1" class="small-text" />
                                <p class="description"><?php _e( 'Controls randomness in AI responses (0.0 = deterministic, 1.0 = very random).', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_mistral_ai_max_tokens"><?php _e( 'Max Tokens', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_mistral_ai_max_tokens" name="sodexo_mistral_ai_max_tokens"
                                       value="<?php echo esc_attr( $max_tokens ); ?>" min="1" max="4000" class="small-text" />
                                <p class="description"><?php _e( 'Maximum number of tokens per AI request.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="card">
                    <h2><?php _e( 'Crawler Configuration', 'sodexo-ai-crawler' ); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_enabled"><?php _e( 'Enable Crawler', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="checkbox" id="sodexo_crawler_enabled" name="sodexo_crawler_enabled"
                                       value="1" <?php checked( $crawler_enabled, 1 ); ?> />
                                <p class="description"><?php _e( 'Enable or disable the crawler functionality.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_interval"><?php _e( 'Cron Interval', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <select id="sodexo_crawler_interval" name="sodexo_crawler_interval">
                                    <option value="hourly" <?php selected( $crawler_interval, 'hourly' ); ?>><?php _e( 'Hourly', 'sodexo-ai-crawler' ); ?></option>
                                    <option value="daily" <?php selected( $crawler_interval, 'daily' ); ?>><?php _e( 'Daily', 'sodexo-ai-crawler' ); ?></option>
                                    <option value="weekly" <?php selected( $crawler_interval, 'weekly' ); ?>><?php _e( 'Weekly', 'sodexo-ai-crawler' ); ?></option>
                                </select>
                                <p class="description"><?php _e( 'How often the crawler should run automatically.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_timeout"><?php _e( 'Request Timeout', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_crawler_timeout" name="sodexo_crawler_timeout"
                                       value="<?php echo esc_attr( $crawler_timeout ); ?>" min="5" max="300" class="small-text" />
                                <span><?php _e( 'seconds', 'sodexo-ai-crawler' ); ?></span>
                                <p class="description"><?php _e( 'Timeout for HTTP requests when crawling websites.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_user_agent"><?php _e( 'User Agent', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="text" id="sodexo_crawler_user_agent" name="sodexo_crawler_user_agent"
                                       value="<?php echo esc_attr( $user_agent ); ?>" class="regular-text" />
                                <p class="description"><?php _e( 'User agent string for web requests.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_batch_size"><?php _e( 'Batch Size', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_crawler_batch_size" name="sodexo_crawler_batch_size"
                                       value="<?php echo esc_attr( $batch_size ); ?>" min="1" max="100" class="small-text" />
                                <p class="description"><?php _e( 'Number of URLs to process per batch.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_max_retries"><?php _e( 'Max Retries', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_crawler_max_retries" name="sodexo_crawler_max_retries"
                                       value="<?php echo esc_attr( $max_retries ); ?>" min="0" max="10" class="small-text" />
                                <p class="description"><?php _e( 'Maximum retry attempts for failed requests.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="card">
                    <h2><?php _e( 'Security & Performance', 'sodexo-ai-crawler' ); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_allowed_domains"><?php _e( 'Allowed Domains', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <textarea id="sodexo_crawler_allowed_domains" name="sodexo_crawler_allowed_domains"
                                          rows="3" class="large-text"><?php echo esc_textarea( $allowed_domains ); ?></textarea>
                                <p class="description"><?php _e( 'Comma-separated list of allowed domains for crawling.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_rate_limit"><?php _e( 'Rate Limit', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_crawler_rate_limit" name="sodexo_crawler_rate_limit"
                                       value="<?php echo esc_attr( $rate_limit ); ?>" min="1" max="60" class="small-text" />
                                <span><?php _e( 'requests per minute', 'sodexo-ai-crawler' ); ?></span>
                                <p class="description"><?php _e( 'Maximum requests per minute to prevent overloading servers.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="card">
                    <h2><?php _e( 'Logging & Debug', 'sodexo-ai-crawler' ); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_log_level"><?php _e( 'Log Level', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <select id="sodexo_crawler_log_level" name="sodexo_crawler_log_level">
                                    <option value="debug" <?php selected( $log_level, 'debug' ); ?>><?php _e( 'Debug', 'sodexo-ai-crawler' ); ?></option>
                                    <option value="info" <?php selected( $log_level, 'info' ); ?>><?php _e( 'Info', 'sodexo-ai-crawler' ); ?></option>
                                    <option value="warning" <?php selected( $log_level, 'warning' ); ?>><?php _e( 'Warning', 'sodexo-ai-crawler' ); ?></option>
                                    <option value="error" <?php selected( $log_level, 'error' ); ?>><?php _e( 'Error', 'sodexo-ai-crawler' ); ?></option>
                                </select>
                                <p class="description"><?php _e( 'Minimum log level to record.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_log_retention_days"><?php _e( 'Log Retention', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="number" id="sodexo_crawler_log_retention_days" name="sodexo_crawler_log_retention_days"
                                       value="<?php echo esc_attr( $log_retention ); ?>" min="1" max="365" class="small-text" />
                                <span><?php _e( 'days', 'sodexo-ai-crawler' ); ?></span>
                                <p class="description"><?php _e( 'Number of days to keep log files.', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="sodexo_crawler_enable_debug"><?php _e( 'Debug Mode', 'sodexo-ai-crawler' ); ?></label>
                            </th>
                            <td>
                                <input type="checkbox" id="sodexo_crawler_enable_debug" name="sodexo_crawler_enable_debug"
                                       value="1" <?php checked( $enable_debug, 1 ); ?> />
                                <p class="description"><?php _e( 'Enable detailed debug logging (may impact performance).', 'sodexo-ai-crawler' ); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <?php submit_button( __( 'Save Settings', 'sodexo-ai-crawler' ) ); ?>
            </form>

            <div class="card">
                <h2><?php _e( 'Manual Crawler Execution', 'sodexo-ai-crawler' ); ?></h2>
                <p><?php _e( 'Click the button below to manually start the crawler process. This will execute the tender_crawler.php script and analyze all partner websites.', 'sodexo-ai-crawler' ); ?></p>

                <button type="button" id="start-crawler-btn" class="button button-primary">
                    <?php _e( 'Start Crawler Now', 'sodexo-ai-crawler' ); ?>
                </button>

                <div id="crawler-output" style="display: none; margin-top: 15px;"></div>

                <div style="margin-top: 15px;">
                    <p><strong><?php _e( 'Note:', 'sodexo-ai-crawler' ); ?></strong></p>
                    <ul>
                        <li><?php _e( 'The crawler will process all URLs from the wp_sodexo_partners table', 'sodexo-ai-crawler' ); ?></li>
                        <li><?php _e( 'Results will be stored in the wp_crawl_results table', 'sodexo-ai-crawler' ); ?></li>
                        <li><?php _e( 'This process may take several minutes depending on the number of partners', 'sodexo-ai-crawler' ); ?></li>
                        <li><?php _e( 'Make sure your Mistral AI API key is configured correctly', 'sodexo-ai-crawler' ); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts( $hook ) {
        // Only load on our settings page
        if ( $hook !== 'tools_page_sodexo-ai-crawler-settings' ) {
            return;
        }

        wp_enqueue_script( 'jquery' );
        wp_add_inline_script( 'jquery', '
            jQuery(document).ready(function($) {
                $("#start-crawler-btn").click(function(e) {
                    e.preventDefault();

                    var button = $(this);
                    var originalText = button.text();

                    // Disable button and show loading state
                    button.prop("disabled", true).text("Starting Crawler...");
                    $("#crawler-output").html("<p>Initializing crawler...</p>").show();

                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        data: {
                            action: "start_crawler",
                            nonce: "' . wp_create_nonce( 'start_crawler_nonce' ) . '"
                        },
                        success: function(response) {
                            if (response.success) {
                                $("#crawler-output").html("<div class=\"notice notice-success\"><p>" + response.data.message + "</p></div>");
                                if (response.data.output) {
                                    $("#crawler-output").append("<div class=\"crawler-log\"><h4>Crawler Output:</h4><pre>" + response.data.output + "</pre></div>");
                                }
                            } else {
                                $("#crawler-output").html("<div class=\"notice notice-error\"><p>Error: " + response.data.message + "</p></div>");
                            }
                        },
                        error: function(xhr, status, error) {
                            $("#crawler-output").html("<div class=\"notice notice-error\"><p>AJAX Error: " + error + "</p></div>");
                        },
                        complete: function() {
                            // Re-enable button
                            button.prop("disabled", false).text(originalText);
                        }
                    });
                });
            });
        ' );

        // Add some basic styling
        wp_add_inline_style( 'wp-admin', '
            .crawler-log {
                margin-top: 15px;
                padding: 10px;
                background: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
            .crawler-log pre {
                max-height: 400px;
                overflow-y: auto;
                background: #fff;
                padding: 10px;
                border: 1px solid #ccc;
                font-size: 12px;
                line-height: 1.4;
            }
            #start-crawler-btn {
                background: #0073aa;
                color: white;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 4px;
                margin: 10px 0;
            }
            #start-crawler-btn:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
        ' );
    }

    /**
     * AJAX handler for starting the crawler
     */
    public function ajax_start_crawler() {
        // Verify nonce
        if ( ! wp_verify_nonce( $_POST['nonce'], 'start_crawler_nonce' ) ) {
            wp_die( 'Security check failed' );
        }

        // Check user capabilities
        if ( ! current_user_can( 'manage_options' ) ) {
            wp_die( 'Insufficient permissions' );
        }

        try {
            // Capture output
            ob_start();

            // Include and execute the crawler
            $crawler_file = SODEXO_AI_CRAWLER_ASSETS_PATH . 'tender_crawler.php';

            if ( ! file_exists( $crawler_file ) ) {
                wp_send_json_error( array( 'message' => 'Crawler file not found: ' . $crawler_file ) );
                return;
            }

            // Set up environment for the crawler
            $_SERVER['REQUEST_METHOD'] = 'GET'; // Simulate CLI environment

            // Execute the crawler in a controlled way
            include $crawler_file;

            $output = ob_get_clean();

            wp_send_json_success( array(
                'message' => 'Crawler executed successfully!',
                'output' => $output
            ) );

        } catch ( Exception $e ) {
            ob_end_clean();
            wp_send_json_error( array(
                'message' => 'Crawler execution failed: ' . $e->getMessage()
            ) );
        }
    }
}

/**
 * Plugin activation hook
 */
function activate_sodexo_ai_crawler() {
    // Create directories and set up initial data
    $plugin = new Sodexo_AI_Crawler();

    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Plugin deactivation hook
 */
function deactivate_sodexo_ai_crawler() {
    // Clean up scheduled events
    wp_clear_scheduled_hook( 'mistral_ai_cron_hook' );

    // Flush rewrite rules
    flush_rewrite_rules();
}

// Register activation and deactivation hooks
register_activation_hook( __FILE__, 'activate_sodexo_ai_crawler' );
register_deactivation_hook( __FILE__, 'deactivate_sodexo_ai_crawler' );

// Initialize the plugin
new Sodexo_AI_Crawler();
